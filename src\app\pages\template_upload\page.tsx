"use client";

import { useState } from "react";
import {
  FileUpload,
  DocumentAnalysis,
  TemplateBuilder,
  TemplatePreview,
} from "./components";

export interface TemplateData {
  id: string;
  name: string;
  description: string;
  originalFile: File | null;
  fileType: "docx" | "xlsx" | "pdf";
  content: string;
  placeholders: PlaceholderData[];
  layout: LayoutData;
  margins: MarginData;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlaceholderData {
  id: string;
  name: string;
  type: "text" | "number" | "date" | "image" | "boolean";
  label: string;
  defaultValue?: string;
  required: boolean;
  position: {
    page: number;
    x: number;
    y: number;
  };
  formatting?: {
    fontSize?: number;
    fontFamily?: string;
    bold?: boolean;
    italic?: boolean;
    color?: string;
  };
}

export interface LayoutData {
  pageSize: "A4" | "Letter" | "Legal";
  orientation: "portrait" | "landscape";
  columns: number;
  headerHeight: number;
  footerHeight: number;
}

export interface MarginData {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface AnalysisResult {
  content: string;
  placeholders: PlaceholderData[];
  layout: LayoutData;
  margins: MarginData;
  images: Array<{
    id: string;
    src: string;
    width: number;
    height: number;
    position: { x: number; y: number };
  }>;
}

type Step = "upload" | "analysis" | "builder" | "preview";

export default function TemplateUploadPage() {
  const [currentStep, setCurrentStep] = useState<Step>("upload");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(
    null
  );
  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileUpload = async (file: File) => {
    setUploadedFile(file);
    setIsProcessing(true);
    setCurrentStep("analysis");

    // This will be implemented in the DocumentAnalysis component
    // For now, just move to the next step after a delay
    setTimeout(() => {
      setIsProcessing(false);
    }, 2000);
  };

  const handleAnalysisComplete = (result: AnalysisResult) => {
    setAnalysisResult(result);
    setCurrentStep("builder");
  };

  const handleTemplateBuilt = (template: TemplateData) => {
    setTemplateData(template);
    setCurrentStep("preview");
  };

  const handleBackToStep = (step: Step) => {
    setCurrentStep(step);
  };

  const handleSaveTemplate = async (template: TemplateData) => {
    // This will be implemented later for template storage
    console.log("Saving template:", template);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-7xl">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-foreground mb-2">
            Template Upload & Creator
          </h1>
          <p className="text-muted-foreground">
            Upload your document templates (Word, Excel) and automatically
            create interactive forms
          </p>
        </div>

        {/* Step Indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between max-w-3xl mx-auto">
            {[
              { key: "upload", label: "Upload File", icon: "📁" },
              { key: "analysis", label: "Analyze Document", icon: "🔍" },
              { key: "builder", label: "Build Template", icon: "🛠️" },
              { key: "preview", label: "Preview & Save", icon: "👁️" },
            ].map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div
                  className={`
                  flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium
                  ${
                    currentStep === step.key
                      ? "bg-primary text-primary-foreground"
                      : index <
                        ["upload", "analysis", "builder", "preview"].indexOf(
                          currentStep
                        )
                      ? "bg-green-500 text-white"
                      : "bg-muted text-muted-foreground"
                  }
                `}
                >
                  {step.icon}
                </div>
                <span className="ml-2 text-sm font-medium text-foreground">
                  {step.label}
                </span>
                {index < 3 && (
                  <div
                    className={`
                    w-16 h-0.5 mx-4
                    ${
                      index <
                      ["upload", "analysis", "builder", "preview"].indexOf(
                        currentStep
                      )
                        ? "bg-green-500"
                        : "bg-muted"
                    }
                  `}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="mt-8">
          {currentStep === "upload" && (
            <FileUpload
              onFileUpload={handleFileUpload}
              isProcessing={isProcessing}
            />
          )}

          {currentStep === "analysis" && (
            <DocumentAnalysis
              file={uploadedFile}
              onAnalysisComplete={handleAnalysisComplete}
              onBack={() => handleBackToStep("upload")}
              isProcessing={isProcessing}
            />
          )}

          {currentStep === "builder" && analysisResult && (
            <TemplateBuilder
              analysisResult={analysisResult}
              originalFile={uploadedFile}
              onTemplateBuilt={handleTemplateBuilt}
              onBack={() => handleBackToStep("analysis")}
            />
          )}

          {currentStep === "preview" && templateData && (
            <TemplatePreview
              templateData={templateData}
              onSave={handleSaveTemplate}
              onBack={() => handleBackToStep("builder")}
              onEdit={() => handleBackToStep("builder")}
            />
          )}
        </div>
      </div>
    </div>
  );
}
