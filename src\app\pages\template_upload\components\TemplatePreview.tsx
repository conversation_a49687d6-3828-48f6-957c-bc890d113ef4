"use client";

import { useState, useRef } from "react";
import {
  ArrowLeft,
  Save,
  Edit,
  Download,
  Eye,
  Play,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Bold,
  Italic,
  Underline,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { TemplateData } from "../page";

interface TemplatePreviewProps {
  templateData: TemplateData;
  onSave: (template: TemplateData) => void;
  onBack: () => void;
  onEdit: () => void;
}

export function TemplatePreview({
  templateData,
  onSave,
  onBack,
  onEdit,
}: TemplatePreviewProps) {
  const [sampleData, setSampleData] = useState<Record<string, string>>(() => {
    const initial: Record<string, string> = {};
    templateData.placeholders.forEach((placeholder) => {
      initial[placeholder.name] =
        placeholder.defaultValue ||
        getSampleValue(placeholder.name, placeholder.type);
    });
    return initial;
  });
  const [showPreview, setShowPreview] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedText, setSelectedText] = useState<string>("");
  const [selectedRange, setSelectedRange] = useState<Range | null>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  function getSampleValue(name: string, type: string): string {
    const lowerName = name.toLowerCase();

    if (type === "date" || lowerName.includes("date")) {
      return new Date().toLocaleDateString();
    }
    if (
      type === "number" ||
      lowerName.includes("age") ||
      lowerName.includes("amount")
    ) {
      return "25";
    }
    if (lowerName.includes("name")) {
      if (lowerName.includes("first")) return "John";
      if (lowerName.includes("last")) return "Doe";
      if (lowerName.includes("middle")) return "Smith";
      return "John Doe";
    }
    if (lowerName.includes("address")) return "123 Main Street, City";
    if (lowerName.includes("phone")) return "+****************";
    if (lowerName.includes("email")) return "<EMAIL>";
    if (lowerName.includes("city")) return "New York";
    if (lowerName.includes("state") || lowerName.includes("province"))
      return "NY";
    if (lowerName.includes("zip") || lowerName.includes("postal"))
      return "10001";
    if (lowerName.includes("company")) return "Acme Corporation";
    if (lowerName.includes("position") || lowerName.includes("title"))
      return "Software Engineer";

    return "Sample Value";
  }

  const handleSampleDataChange = (fieldName: string, value: string) => {
    setSampleData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  const generatePreviewContent = () => {
    let content = templateData.content;

    // If content is empty or very basic, provide a better example structure
    if (!content || content.trim().length < 50) {
      content = `Republic of the Philippines
Province of {province}
MUNICIPALITY OF {municipality}

OFFICE OF THE MAYOR

CERTIFICATE OF GOOD MORAL CHARACTER

TO WHOM IT MAY CONCERN:

This is to certify that {name}, {age} years old, of legal age, single/married, Filipino citizen and a resident of {address}, {municipality}, {province} has been known to me to be of good moral character.

This certification is issued upon the request of the above-mentioned person for whatever legal purpose it may serve.

Given this {date} at {municipality}, {province}.

                                    {mayor_name}
                                    Municipal Mayor`;
    }

    templateData.placeholders.forEach((placeholder) => {
      const value = sampleData[placeholder.name] || "";
      const formatting = placeholder.formatting;

      // Create styled span for the value
      let styledValue = value;
      if (formatting) {
        const styles = [];
        if (formatting.bold) styles.push("font-weight: bold");
        if (formatting.italic) styles.push("font-style: italic");
        if (formatting.underline) styles.push("text-decoration: underline");
        if (formatting.fontSize)
          styles.push(`font-size: ${formatting.fontSize}px`);
        if (formatting.color) styles.push(`color: ${formatting.color}`);
        if (formatting.fontFamily)
          styles.push(`font-family: ${formatting.fontFamily}`);
        if (formatting.textAlign)
          styles.push(`text-align: ${formatting.textAlign}`);
        if (
          formatting.backgroundColor &&
          formatting.backgroundColor !== "transparent"
        ) {
          styles.push(`background-color: ${formatting.backgroundColor}`);
        }

        if (styles.length > 0) {
          styledValue = `<span style="${styles.join("; ")}">${value}</span>`;
        }
      }

      const regex = new RegExp(`\\{${placeholder.name}\\}`, "g");
      content = content.replace(regex, styledValue);
    });

    return content;
  };

  const handleSaveTemplate = () => {
    onSave(templateData);
    toast.success(`Template "${templateData.name}" saved successfully!`);
  };

  const handleDownloadTemplate = () => {
    // Create a JSON file with the template data
    const templateJson = JSON.stringify(templateData, null, 2);
    const blob = new Blob([templateJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `${templateData.name.replace(/\s+/g, "_")}_template.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
    toast.success("Template downloaded successfully!");
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = selection.toString().trim();

      if (
        selectedText &&
        selectedText.length > 0 &&
        previewRef.current?.contains(range.commonAncestorContainer)
      ) {
        setSelectedText(selectedText);
        setSelectedRange(range);
        console.log("Selected text:", selectedText); // Debug log
      } else {
        // Clear selection if no valid text is selected
        setSelectedText("");
        setSelectedRange(null);
      }
    } else {
      // Clear selection if nothing is selected
      setSelectedText("");
      setSelectedRange(null);
    }
  };

  const clearSelection = () => {
    setSelectedText("");
    setSelectedRange(null);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
    }
  };

  const applyFormatting = (command: string, value?: string) => {
    if (!selectedRange) return;

    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(selectedRange);

      try {
        document.execCommand(command, false, value);

        // Update the selection after formatting
        const newSelection = window.getSelection();
        if (newSelection && newSelection.rangeCount > 0) {
          setSelectedRange(newSelection.getRangeAt(0));
        }
      } catch (error) {
        console.error("Error applying formatting:", error);
      }
    }
  };

  const applyAlignment = (
    alignment: "left" | "center" | "right" | "justify"
  ) => {
    if (!selectedRange) return;

    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(selectedRange);

      // Find the parent block element
      let element = selectedRange.commonAncestorContainer;
      if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement!;
      }

      // Apply text alignment
      (element as HTMLElement).style.textAlign = alignment;
    }
  };

  const saveFormattingChanges = () => {
    if (!previewRef.current) return;

    // Extract the formatted HTML content
    const formattedContent = previewRef.current.innerHTML;

    // Here you would typically parse the HTML and update the template data
    // For now, we'll just show a success message
    toast.success("Formatting changes saved to template!");

    // Exit edit mode
    setIsEditMode(false);
    setSelectedText("");
    setSelectedRange(null);
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <Button variant="ghost" onClick={onBack} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Builder
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Info & Sample Data */}
        <div className="lg:col-span-1 space-y-6">
          {/* Template Information */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                Template Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Name
                </Label>
                <p className="text-foreground font-medium">
                  {templateData.name}
                </p>
              </div>
              {templateData.description && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Description
                  </Label>
                  <p className="text-foreground">{templateData.description}</p>
                </div>
              )}
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  File Type
                </Label>
                <p className="text-foreground uppercase">
                  {templateData.fileType}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Placeholders
                </Label>
                <p className="text-foreground">
                  {templateData.placeholders.length} fields
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Layout
                </Label>
                <p className="text-foreground">
                  {templateData.layout.pageSize} •{" "}
                  {templateData.layout.orientation}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Sample Data Input */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Play className="w-5 h-5" />
                Test Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground mb-4">
                Enter sample data to preview how your template will look
              </p>
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {templateData.placeholders.map((placeholder) => (
                  <div key={placeholder.id} className="space-y-1">
                    <Label className="text-sm font-medium">
                      {placeholder.label}
                      {placeholder.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </Label>
                    {placeholder.type === "date" ? (
                      <Input
                        type="date"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      />
                    ) : placeholder.type === "number" ? (
                      <Input
                        type="number"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      />
                    ) : placeholder.type === "boolean" ? (
                      <select
                        className="w-full p-2 border border-border rounded-md bg-background"
                        value={sampleData[placeholder.name] || "false"}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      >
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                      </select>
                    ) : (
                      <Input
                        type="text"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                        placeholder={`Enter ${placeholder.label.toLowerCase()}`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <Button
                onClick={() => setShowPreview(true)}
                className="w-full"
                variant="outline"
              >
                <Eye className="w-4 h-4 mr-2" />
                Generate Preview
              </Button>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="space-y-3">
            <Button onClick={handleSaveTemplate} className="w-full">
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </Button>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" onClick={handleDownloadTemplate}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Preview Area */}
        <div className="lg:col-span-2">
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Template Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {showPreview ? (
                <div className="space-y-4">
                  {/* Selection Instructions */}
                  {isEditMode && !selectedText && (
                    <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <Eye className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                        <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          Select Text to Format
                        </span>
                      </div>
                      <p className="text-xs text-yellow-600 dark:text-yellow-300 mt-1">
                        Highlight any text in the document below (like "Republic
                        of the Philippines") to format it.
                      </p>
                    </div>
                  )}

                  {/* Formatting Toolbar */}
                  {isEditMode && selectedText && (
                    <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          <span className="text-sm font-medium text-foreground">
                            Selected Text:
                          </span>
                        </div>
                        <div className="bg-white dark:bg-gray-800 px-2 py-1 rounded border text-sm font-mono">
                          "
                          {selectedText.length > 50
                            ? selectedText.substring(0, 50) + "..."
                            : selectedText}
                          "
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={clearSelection}
                          className="ml-auto"
                        >
                          Clear
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {/* Text Style Buttons */}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyFormatting("bold")}
                        >
                          <Bold className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyFormatting("italic")}
                        >
                          <Italic className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyFormatting("underline")}
                        >
                          <Underline className="w-4 h-4" />
                        </Button>

                        <div className="w-px h-6 bg-border mx-1" />

                        {/* Alignment Buttons */}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyAlignment("left")}
                        >
                          <AlignLeft className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyAlignment("center")}
                        >
                          <AlignCenter className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyAlignment("right")}
                        >
                          <AlignRight className="w-4 h-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyAlignment("justify")}
                        >
                          <AlignJustify className="w-4 h-4" />
                        </Button>

                        <div className="w-px h-6 bg-border mx-1" />

                        {/* Save Changes Button */}
                        <Button
                          type="button"
                          variant="default"
                          size="sm"
                          onClick={saveFormattingChanges}
                        >
                          <Save className="w-4 h-4 mr-1" />
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Edit Mode Indicator */}
                  {isEditMode && (
                    <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          Edit Mode Active
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                        💡 <strong>Tip:</strong> Click and drag to select text
                        like "Republic of the Philippines", then use the
                        formatting toolbar.
                      </p>
                    </div>
                  )}

                  {/* Preview Document */}
                  <div
                    ref={previewRef}
                    className={`bg-white border rounded-lg p-8 min-h-[600px] shadow-sm transition-all duration-200 ${
                      isEditMode
                        ? "border-blue-300 ring-2 ring-blue-100 dark:ring-blue-900/20 cursor-text [&::selection]:bg-blue-200 [&::selection]:text-blue-900"
                        : "border-gray-300"
                    }`}
                    style={{
                      fontFamily: "Arial, sans-serif",
                      fontSize: "12px",
                      lineHeight: "1.8",
                      color: "#000000",
                      userSelect: isEditMode ? "text" : "none",
                    }}
                    contentEditable={isEditMode}
                    suppressContentEditableWarning={true}
                    onMouseUp={handleTextSelection}
                    onKeyUp={handleTextSelection}
                    onClick={
                      isEditMode
                        ? undefined
                        : () => {
                            if (!isEditMode) {
                              toast.info(
                                "Click 'Edit Format' to select and format text"
                              );
                            }
                          }
                    }
                  >
                    <div
                      className="whitespace-pre-wrap"
                      dangerouslySetInnerHTML={{
                        __html: generatePreviewContent(),
                      }}
                    />
                  </div>

                  {/* Preview Actions */}
                  <div className="flex justify-center gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowPreview(false)}
                    >
                      Edit Data
                    </Button>
                    <Button
                      variant={isEditMode ? "default" : "outline"}
                      onClick={() => {
                        setIsEditMode(!isEditMode);
                        if (!isEditMode) {
                          setSelectedText("");
                          setSelectedRange(null);
                        }
                      }}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      {isEditMode ? "Exit Edit" : "Edit Format"}
                    </Button>
                    <Button
                      onClick={() => {
                        // In a real implementation, this would generate and download the actual document
                        toast.info("Document generation feature coming soon!");
                      }}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Document
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-20">
                  <Eye className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Ready to Preview
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Fill in the test data on the left and click &quot;Generate
                    Preview&quot; to see how your template will look
                  </p>
                  <Button onClick={() => setShowPreview(true)} size="lg">
                    <Eye className="w-5 h-5 mr-2" />
                    Generate Preview
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Template Summary */}
      <Card className="bg-card border-border mt-6">
        <CardHeader>
          <CardTitle className="text-card-foreground">
            Template Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {templateData.placeholders.length}
              </p>
              <p className="text-sm text-muted-foreground">Form Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {templateData.placeholders.filter((p) => p.required).length}
              </p>
              <p className="text-sm text-muted-foreground">Required Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {
                  templateData.placeholders.filter((p) => p.type === "image")
                    .length
                }
              </p>
              <p className="text-sm text-muted-foreground">Image Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">1</p>
              <p className="text-sm text-muted-foreground">Pages</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
