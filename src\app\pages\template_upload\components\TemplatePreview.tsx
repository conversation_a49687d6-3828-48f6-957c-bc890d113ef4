"use client";

import { useState } from "react";
import { ArrowLeft, Save, Edit, Download, Eye, Play } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { TemplateData } from "../page";

interface TemplatePreviewProps {
  templateData: TemplateData;
  onSave: (template: TemplateData) => void;
  onBack: () => void;
  onEdit: () => void;
}

export function TemplatePreview({
  templateData,
  onSave,
  onBack,
  onEdit,
}: TemplatePreviewProps) {
  const [sampleData, setSampleData] = useState<Record<string, string>>(() => {
    const initial: Record<string, string> = {};
    templateData.placeholders.forEach((placeholder) => {
      initial[placeholder.name] =
        placeholder.defaultValue ||
        getSampleValue(placeholder.name, placeholder.type);
    });
    return initial;
  });
  const [showPreview, setShowPreview] = useState(false);

  function getSampleValue(name: string, type: string): string {
    const lowerName = name.toLowerCase();

    if (type === "date" || lowerName.includes("date")) {
      return new Date().toLocaleDateString();
    }
    if (
      type === "number" ||
      lowerName.includes("age") ||
      lowerName.includes("amount")
    ) {
      return "25";
    }
    if (lowerName.includes("name")) {
      if (lowerName.includes("first")) return "John";
      if (lowerName.includes("last")) return "Doe";
      if (lowerName.includes("middle")) return "Smith";
      return "John Doe";
    }
    if (lowerName.includes("address")) return "123 Main Street, City";
    if (lowerName.includes("phone")) return "+****************";
    if (lowerName.includes("email")) return "<EMAIL>";
    if (lowerName.includes("city")) return "New York";
    if (lowerName.includes("state") || lowerName.includes("province"))
      return "NY";
    if (lowerName.includes("zip") || lowerName.includes("postal"))
      return "10001";
    if (lowerName.includes("company")) return "Acme Corporation";
    if (lowerName.includes("position") || lowerName.includes("title"))
      return "Software Engineer";

    return "Sample Value";
  }

  const handleSampleDataChange = (fieldName: string, value: string) => {
    setSampleData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  const generatePreviewContent = () => {
    let content = templateData.content;

    templateData.placeholders.forEach((placeholder) => {
      const value = sampleData[placeholder.name] || "";
      const formatting = placeholder.formatting;

      // Create styled span for the value
      let styledValue = value;
      if (formatting) {
        const styles = [];
        if (formatting.bold) styles.push("font-weight: bold");
        if (formatting.italic) styles.push("font-style: italic");
        if (formatting.underline) styles.push("text-decoration: underline");
        if (formatting.fontSize)
          styles.push(`font-size: ${formatting.fontSize}px`);
        if (formatting.color) styles.push(`color: ${formatting.color}`);
        if (formatting.fontFamily)
          styles.push(`font-family: ${formatting.fontFamily}`);
        if (formatting.textAlign)
          styles.push(`text-align: ${formatting.textAlign}`);
        if (
          formatting.backgroundColor &&
          formatting.backgroundColor !== "transparent"
        ) {
          styles.push(`background-color: ${formatting.backgroundColor}`);
        }

        if (styles.length > 0) {
          styledValue = `<span style="${styles.join("; ")}">${value}</span>`;
        }
      }

      const regex = new RegExp(`\\{${placeholder.name}\\}`, "g");
      content = content.replace(regex, styledValue);
    });

    return content;
  };

  const handleSaveTemplate = () => {
    onSave(templateData);
    toast.success(`Template "${templateData.name}" saved successfully!`);
  };

  const handleDownloadTemplate = () => {
    // Create a JSON file with the template data
    const templateJson = JSON.stringify(templateData, null, 2);
    const blob = new Blob([templateJson], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `${templateData.name.replace(/\s+/g, "_")}_template.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
    toast.success("Template downloaded successfully!");
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <Button variant="ghost" onClick={onBack} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Builder
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Info & Sample Data */}
        <div className="lg:col-span-1 space-y-6">
          {/* Template Information */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                Template Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Name
                </Label>
                <p className="text-foreground font-medium">
                  {templateData.name}
                </p>
              </div>
              {templateData.description && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Description
                  </Label>
                  <p className="text-foreground">{templateData.description}</p>
                </div>
              )}
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  File Type
                </Label>
                <p className="text-foreground uppercase">
                  {templateData.fileType}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Placeholders
                </Label>
                <p className="text-foreground">
                  {templateData.placeholders.length} fields
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Layout
                </Label>
                <p className="text-foreground">
                  {templateData.layout.pageSize} •{" "}
                  {templateData.layout.orientation}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Sample Data Input */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Play className="w-5 h-5" />
                Test Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground mb-4">
                Enter sample data to preview how your template will look
              </p>
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {templateData.placeholders.map((placeholder) => (
                  <div key={placeholder.id} className="space-y-1">
                    <Label className="text-sm font-medium">
                      {placeholder.label}
                      {placeholder.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </Label>
                    {placeholder.type === "date" ? (
                      <Input
                        type="date"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      />
                    ) : placeholder.type === "number" ? (
                      <Input
                        type="number"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      />
                    ) : placeholder.type === "boolean" ? (
                      <select
                        className="w-full p-2 border border-border rounded-md bg-background"
                        value={sampleData[placeholder.name] || "false"}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                      >
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                      </select>
                    ) : (
                      <Input
                        type="text"
                        value={sampleData[placeholder.name] || ""}
                        onChange={(e) =>
                          handleSampleDataChange(
                            placeholder.name,
                            e.target.value
                          )
                        }
                        placeholder={`Enter ${placeholder.label.toLowerCase()}`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <Button
                onClick={() => setShowPreview(true)}
                className="w-full"
                variant="outline"
              >
                <Eye className="w-4 h-4 mr-2" />
                Generate Preview
              </Button>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="space-y-3">
            <Button onClick={handleSaveTemplate} className="w-full">
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </Button>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" onClick={handleDownloadTemplate}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Preview Area */}
        <div className="lg:col-span-2">
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Template Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {showPreview ? (
                <div className="space-y-4">
                  {/* Preview Document */}
                  <div
                    className="bg-white border border-gray-300 rounded-lg p-8 min-h-[600px] shadow-sm"
                    style={{
                      fontFamily: "Arial, sans-serif",
                      fontSize: "12px",
                      lineHeight: "1.5",
                      color: "#000000",
                    }}
                  >
                    <div
                      className="whitespace-pre-wrap"
                      dangerouslySetInnerHTML={{
                        __html: generatePreviewContent(),
                      }}
                    />
                  </div>

                  {/* Preview Actions */}
                  <div className="flex justify-center gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowPreview(false)}
                    >
                      Edit Data
                    </Button>
                    <Button
                      onClick={() => {
                        // In a real implementation, this would generate and download the actual document
                        toast.info("Document generation feature coming soon!");
                      }}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Document
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-20">
                  <Eye className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Ready to Preview
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Fill in the test data on the left and click &quot;Generate
                    Preview&quot; to see how your template will look
                  </p>
                  <Button onClick={() => setShowPreview(true)} size="lg">
                    <Eye className="w-5 h-5 mr-2" />
                    Generate Preview
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Template Summary */}
      <Card className="bg-card border-border mt-6">
        <CardHeader>
          <CardTitle className="text-card-foreground">
            Template Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {templateData.placeholders.length}
              </p>
              <p className="text-sm text-muted-foreground">Form Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {templateData.placeholders.filter((p) => p.required).length}
              </p>
              <p className="text-sm text-muted-foreground">Required Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">
                {
                  templateData.placeholders.filter((p) => p.type === "image")
                    .length
                }
              </p>
              <p className="text-sm text-muted-foreground">Image Fields</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold text-primary">1</p>
              <p className="text-sm text-muted-foreground">Pages</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
