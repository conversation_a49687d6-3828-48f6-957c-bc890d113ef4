"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, Eye, Save, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { AnalysisResult, TemplateData, PlaceholderData } from "../page";

interface TemplateBuilderProps {
  analysisResult: AnalysisResult;
  originalFile: File | null;
  onTemplateBuilt: (template: TemplateData) => void;
  onBack: () => void;
}

export function TemplateBuilder({
  analysisResult,
  originalFile,
  onTemplateBuilt,
  onBack,
}: TemplateBuilderProps) {
  const [templateName, setTemplateName] = useState(
    originalFile?.name.replace(/\.[^/.]+$/, "") || "New Template"
  );
  const [templateDescription, setTemplateDescription] = useState("");
  const [placeholders, setPlaceholders] = useState<PlaceholderData[]>(
    analysisResult.placeholders
  );
  const [selectedPlaceholder, setSelectedPlaceholder] = useState<string | null>(null);

  const handlePlaceholderUpdate = (id: string, updates: Partial<PlaceholderData>) => {
    setPlaceholders(prev =>
      prev.map(placeholder =>
        placeholder.id === id ? { ...placeholder, ...updates } : placeholder
      )
    );
  };

  const handleAddPlaceholder = () => {
    const newPlaceholder: PlaceholderData = {
      id: `placeholder_${Date.now()}`,
      name: "newField",
      type: "text",
      label: "New Field",
      required: false,
      position: {
        page: 1,
        x: 100,
        y: 200,
      },
      formatting: {
        fontSize: 12,
        fontFamily: "Arial",
        bold: false,
        italic: false,
        color: "#000000",
      },
    };
    setPlaceholders(prev => [...prev, newPlaceholder]);
  };

  const handleRemovePlaceholder = (id: string) => {
    setPlaceholders(prev => prev.filter(p => p.id !== id));
    if (selectedPlaceholder === id) {
      setSelectedPlaceholder(null);
    }
  };

  const handleBuildTemplate = () => {
    if (!templateName.trim()) {
      toast.error("Please enter a template name");
      return;
    }

    if (placeholders.length === 0) {
      toast.error("Template must have at least one placeholder");
      return;
    }

    const template: TemplateData = {
      id: `template_${Date.now()}`,
      name: templateName,
      description: templateDescription,
      originalFile,
      fileType: originalFile?.type.includes('word') ? 'docx' :
                originalFile?.type.includes('sheet') ? 'xlsx' : 'pdf',
      content: analysisResult.content,
      placeholders,
      layout: analysisResult.layout,
      margins: analysisResult.margins,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    toast.success("Template built successfully!");
    onTemplateBuilt(template);
  };

  const selectedPlaceholderData = placeholders.find(p => p.id === selectedPlaceholder);

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <Button variant="ghost" onClick={onBack} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Analysis
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Settings */}
        <div className="lg:col-span-1">
          <Card className="bg-card border-border mb-6">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Template Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="template-description">Description</Label>
                <Input
                  id="template-description"
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="Optional description"
                />
              </div>
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-foreground">Placeholders</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddPlaceholder}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add
                  </Button>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {placeholders.map((placeholder) => (
                    <div
                      key={placeholder.id}
                      className={`
                        p-3 rounded-lg border cursor-pointer transition-all
                        ${selectedPlaceholder === placeholder.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                        }
                      `}
                      onClick={() => setSelectedPlaceholder(placeholder.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm text-foreground">
                            {"{" + placeholder.name + "}"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {placeholder.type} • {placeholder.required ? 'Required' : 'Optional'}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemovePlaceholder(placeholder.id);
                          }}
                          className="text-muted-foreground hover:text-destructive"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => toast.info("Preview functionality coming soon!")}
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button
              onClick={handleBuildTemplate}
              className="flex-1"
            >
              <Save className="w-4 h-4 mr-2" />
              Build Template
            </Button>
          </div>
        </div>

        {/* Placeholder Configuration */}
        <div className="lg:col-span-2">
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                {selectedPlaceholderData 
                  ? `Configure: {${selectedPlaceholderData.name}}`
                  : "Select a placeholder to configure"
                }
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedPlaceholderData ? (
                <div className="space-y-6">
                  {/* Basic Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Field Name</Label>
                      <Input
                        value={selectedPlaceholderData.name}
                        onChange={(e) =>
                          handlePlaceholderUpdate(selectedPlaceholderData.id, {
                            name: e.target.value,
                          })
                        }
                        placeholder="fieldName"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Display Label</Label>
                      <Input
                        value={selectedPlaceholderData.label}
                        onChange={(e) =>
                          handlePlaceholderUpdate(selectedPlaceholderData.id, {
                            label: e.target.value,
                          })
                        }
                        placeholder="Field Label"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Field Type</Label>
                      <Select
                        value={selectedPlaceholderData.type}
                        onValueChange={(value: PlaceholderData['type']) =>
                          handlePlaceholderUpdate(selectedPlaceholderData.id, {
                            type: value,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="number">Number</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="image">Image</SelectItem>
                          <SelectItem value="boolean">Boolean</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Default Value</Label>
                      <Input
                        value={selectedPlaceholderData.defaultValue || ""}
                        onChange={(e) =>
                          handlePlaceholderUpdate(selectedPlaceholderData.id, {
                            defaultValue: e.target.value,
                          })
                        }
                        placeholder="Optional default value"
                      />
                    </div>
                  </div>

                  {/* Formatting Options */}
                  <div className="border-t pt-6">
                    <h4 className="font-medium text-foreground mb-4">Text Formatting</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label>Font Size</Label>
                        <Input
                          type="number"
                          value={selectedPlaceholderData.formatting?.fontSize || 12}
                          onChange={(e) =>
                            handlePlaceholderUpdate(selectedPlaceholderData.id, {
                              formatting: {
                                ...selectedPlaceholderData.formatting,
                                fontSize: parseInt(e.target.value),
                              },
                            })
                          }
                          min="8"
                          max="72"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Font Family</Label>
                        <Select
                          value={selectedPlaceholderData.formatting?.fontFamily || "Arial"}
                          onValueChange={(value) =>
                            handlePlaceholderUpdate(selectedPlaceholderData.id, {
                              formatting: {
                                ...selectedPlaceholderData.formatting,
                                fontFamily: value,
                              },
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Arial">Arial</SelectItem>
                            <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                            <SelectItem value="Calibri">Calibri</SelectItem>
                            <SelectItem value="Helvetica">Helvetica</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Text Color</Label>
                        <Input
                          type="color"
                          value={selectedPlaceholderData.formatting?.color || "#000000"}
                          onChange={(e) =>
                            handlePlaceholderUpdate(selectedPlaceholderData.id, {
                              formatting: {
                                ...selectedPlaceholderData.formatting,
                                color: e.target.value,
                              },
                            })
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Required</Label>
                        <Select
                          value={selectedPlaceholderData.required ? "true" : "false"}
                          onValueChange={(value) =>
                            handlePlaceholderUpdate(selectedPlaceholderData.id, {
                              required: value === "true",
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="true">Required</SelectItem>
                            <SelectItem value="false">Optional</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Select a placeholder from the left panel to configure its properties
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
