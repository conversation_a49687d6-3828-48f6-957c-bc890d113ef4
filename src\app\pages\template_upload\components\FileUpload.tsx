"use client";

import { useState, useCallback } from "react";
import { Upload, FileText, FileSpreadsheet, File, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

interface FileUploadProps {
  onFileUpload: (file: File) => void;
  isProcessing: boolean;
}

export function FileUpload({ onFileUpload, isProcessing }: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const acceptedTypes = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      ".docx",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      ".xlsx",
    "application/pdf": ".pdf",
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes("word") || fileType.includes("document")) {
      return <FileText className="w-8 h-8 text-blue-500" />;
    }
    if (fileType.includes("sheet") || fileType.includes("excel")) {
      return <FileSpreadsheet className="w-8 h-8 text-green-500" />;
    }
    if (fileType.includes("pdf")) {
      return <File className="w-8 h-8 text-red-500" />;
    }
    return <File className="w-8 h-8 text-gray-500" />;
  };

  const handleFileSelect = useCallback(
    (file: File) => {
      const validateFile = (file: File): boolean => {
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (file.size > maxSize) {
          toast.error("File size must be less than 10MB");
          return false;
        }

        if (!Object.keys(acceptedTypes).includes(file.type)) {
          toast.error(
            "Please upload a Word document (.docx), Excel file (.xlsx), or PDF (.pdf)"
          );
          return false;
        }

        return true;
      };

      if (validateFile(file)) {
        setSelectedFile(file);
        toast.success(`File "${file.name}" selected successfully!`);
      }
    },
    [acceptedTypes]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = () => {
    if (selectedFile) {
      onFileUpload(selectedFile);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-card-foreground flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload Document Template
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!selectedFile ? (
            <div
              className={`
                relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300
                ${
                  isDragOver
                    ? "border-primary bg-primary/10"
                    : "border-border hover:border-primary/50"
                }
              `}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="space-y-6">
                <Upload className="w-16 h-16 text-muted-foreground mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Upload your document template
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Drag and drop your file here, or click to browse
                  </p>
                  <div className="flex flex-wrap justify-center gap-2 text-sm text-muted-foreground">
                    <span className="bg-muted px-2 py-1 rounded">
                      Word (.docx)
                    </span>
                    <span className="bg-muted px-2 py-1 rounded">
                      Excel (.xlsx)
                    </span>
                    <span className="bg-muted px-2 py-1 rounded">
                      PDF (.pdf)
                    </span>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="lg"
                  onClick={() => document.getElementById("file-input")?.click()}
                >
                  Browse Files
                </Button>
              </div>
              <input
                id="file-input"
                type="file"
                accept={Object.keys(acceptedTypes).join(",")}
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          ) : (
            <div className="space-y-6">
              {/* Selected File Display */}
              <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border">
                <div className="flex items-center gap-3">
                  {getFileIcon(selectedFile.type)}
                  <div>
                    <p className="font-medium text-foreground">
                      {selectedFile.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {/* File Info */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center p-3 bg-muted/30 rounded-lg">
                  <p className="text-muted-foreground">File Type</p>
                  <p className="font-medium text-foreground">
                    {selectedFile.type.includes("word")
                      ? "Word Document"
                      : selectedFile.type.includes("sheet")
                      ? "Excel Spreadsheet"
                      : selectedFile.type.includes("pdf")
                      ? "PDF Document"
                      : "Unknown"}
                  </p>
                </div>
                <div className="text-center p-3 bg-muted/30 rounded-lg">
                  <p className="text-muted-foreground">File Size</p>
                  <p className="font-medium text-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <div className="text-center p-3 bg-muted/30 rounded-lg">
                  <p className="text-muted-foreground">Last Modified</p>
                  <p className="font-medium text-foreground">
                    {new Date(selectedFile.lastModified).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {/* Upload Actions */}
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => document.getElementById("file-input")?.click()}
                >
                  Choose Different File
                </Button>
                <Button
                  onClick={handleUpload}
                  disabled={isProcessing}
                  className="min-w-[120px]"
                >
                  {isProcessing ? "Processing..." : "Analyze Document"}
                </Button>
              </div>

              <input
                id="file-input"
                type="file"
                accept={Object.keys(acceptedTypes).join(",")}
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          )}

          {/* Help Text */}
          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              What happens next?
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Your document will be scanned for content and layout</li>
              <li>
                • Placeholders like {"{name}"}, {"{date}"} will be automatically
                detected
              </li>
              <li>
                • You&apos;ll be able to configure form fields for each
                placeholder
              </li>
              <li>
                • A preview will be generated showing how the template works
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
