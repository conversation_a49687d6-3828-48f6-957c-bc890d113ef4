"use client";

import { useState, useEffect, useCallback } from "react";
import {
  ArrowLeft,
  Search,
  FileText,
  Layout,
  Image as ImageIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import {
  AnalysisResult,
  PlaceholderData,
  LayoutData,
  MarginData,
} from "../page";
import mammoth from "mammoth";

interface DocumentAnalysisProps {
  file: File | null;
  onAnalysisComplete: (result: AnalysisResult) => void;
  onBack: () => void;
  isProcessing: boolean;
}

export function DocumentAnalysis({
  file,
  onAnalysisComplete,
  onBack,
  isProcessing,
}: DocumentAnalysisProps) {
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState("");
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(
    null
  );

  const analyzeDocument = useCallback(async () => {
    if (!file) return;

    try {
      setCurrentStep("Reading document content...");
      setAnalysisProgress(20);

      let content = "";
      let placeholders: PlaceholderData[] = [];

      if (file.type.includes("word")) {
        content = await analyzeWordDocument(file);
      } else if (file.type.includes("sheet")) {
        content = await analyzeExcelDocument();
      } else if (file.type.includes("pdf")) {
        content = await analyzePDFDocument();
      }

      setCurrentStep("Detecting placeholders...");
      setAnalysisProgress(40);

      // Extract placeholders from content
      placeholders = extractPlaceholders(content);

      setCurrentStep("Analyzing layout and margins...");
      setAnalysisProgress(60);

      // Analyze layout (mock implementation)
      const layout: LayoutData = {
        pageSize: "A4",
        orientation: "portrait",
        columns: 1,
        headerHeight: 72,
        footerHeight: 72,
      };

      const margins: MarginData = {
        top: 72,
        bottom: 72,
        left: 72,
        right: 72,
      };

      setCurrentStep("Processing images...");
      setAnalysisProgress(80);

      // Extract images (mock implementation)
      const images = extractImages(content);

      setCurrentStep("Finalizing analysis...");
      setAnalysisProgress(100);

      const result: AnalysisResult = {
        content,
        placeholders,
        layout,
        margins,
        images,
      };

      setAnalysisResult(result);

      setTimeout(() => {
        toast.success("Document analysis completed successfully!");
        onAnalysisComplete(result);
      }, 1000);
    } catch (error) {
      console.error("Analysis error:", error);
      toast.error("Failed to analyze document. Please try again.");
    }
  }, [file, onAnalysisComplete]);

  useEffect(() => {
    if (file && !isProcessing) {
      analyzeDocument();
    }
  }, [file, isProcessing, analyzeDocument]);

  const analyzeWordDocument = async (file: File): Promise<string> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      return result.value;
    } catch (error) {
      console.error("Word document analysis error:", error);
      throw new Error("Failed to analyze Word document");
    }
  };

  const analyzeExcelDocument = async (): Promise<string> => {
    // For Excel files, we would need a library like xlsx
    // For now, return a mock content
    return "Excel content analysis not yet implemented. Placeholder: {name}, {date}, {amount}";
  };

  const analyzePDFDocument = async (): Promise<string> => {
    // For PDF files, we would need a library like pdf-parse
    // For now, return a mock content
    return "PDF content analysis not yet implemented. Placeholder: {name}, {date}, {signature}";
  };

  const extractPlaceholders = (content: string): PlaceholderData[] => {
    const placeholderRegex = /\{([^}]+)\}/g;
    const matches = content.match(placeholderRegex) || [];
    const uniquePlaceholders = [...new Set(matches)];

    return uniquePlaceholders.map((placeholder, index) => {
      const name = placeholder.replace(/[{}]/g, "");

      // Determine type based on placeholder name
      let type: PlaceholderData["type"] = "text";
      if (name.toLowerCase().includes("date")) type = "date";
      if (
        name.toLowerCase().includes("age") ||
        name.toLowerCase().includes("number")
      )
        type = "number";
      if (
        name.toLowerCase().includes("image") ||
        name.toLowerCase().includes("photo")
      )
        type = "image";
      if (
        name.toLowerCase().includes("check") ||
        name.toLowerCase().includes("bool")
      )
        type = "boolean";

      return {
        id: `placeholder_${index}`,
        name,
        type,
        label:
          name.charAt(0).toUpperCase() +
          name.slice(1).replace(/([A-Z])/g, " $1"),
        required: true,
        position: {
          page: 1,
          x: 100 + index * 50,
          y: 200 + index * 30,
        },
        formatting: {
          fontSize: 12,
          fontFamily: "Arial",
          bold: false,
          italic: false,
          color: "#000000",
        },
      };
    });
  };

  const extractImages = (content: string) => {
    // Mock image extraction - in real implementation, this would extract actual images
    const imageMatches = content.match(/\{[^}]*image[^}]*\}/gi) || [];

    return imageMatches.map((_match, index) => ({
      id: `image_${index}`,
      src: "", // Would contain actual image data
      width: 100,
      height: 100,
      position: { x: 50, y: 50 + index * 120 },
    }));
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Button variant="ghost" onClick={onBack} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Upload
        </Button>
      </div>

      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-card-foreground flex items-center gap-2">
            <Search className="w-5 h-5" />
            Analyzing Document: {file?.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-foreground">
                Analysis Progress
              </span>
              <span className="text-sm text-muted-foreground">
                {analysisProgress}%
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-500"
                style={{ width: `${analysisProgress}%` }}
              />
            </div>
            <p className="text-sm text-muted-foreground mt-2">{currentStep}</p>
          </div>

          {/* Analysis Steps */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {[
              {
                icon: FileText,
                label: "Content",
                completed: analysisProgress > 20,
              },
              {
                icon: Search,
                label: "Placeholders",
                completed: analysisProgress > 40,
              },
              {
                icon: Layout,
                label: "Layout",
                completed: analysisProgress > 60,
              },
              {
                icon: ImageIcon,
                label: "Images",
                completed: analysisProgress > 80,
              },
            ].map((step, index) => (
              <div
                key={index}
                className={`
                  p-4 rounded-lg border text-center transition-all duration-300
                  ${
                    step.completed
                      ? "bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800"
                      : "bg-muted/30 border-border"
                  }
                `}
              >
                <step.icon
                  className={`w-8 h-8 mx-auto mb-2 ${
                    step.completed
                      ? "text-green-600 dark:text-green-400"
                      : "text-muted-foreground"
                  }`}
                />
                <p
                  className={`text-sm font-medium ${
                    step.completed
                      ? "text-green-800 dark:text-green-200"
                      : "text-muted-foreground"
                  }`}
                >
                  {step.label}
                </p>
              </div>
            ))}
          </div>

          {/* Analysis Results Preview */}
          {analysisResult && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground">
                Analysis Results
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-medium text-foreground mb-2">
                    Placeholders Found
                  </h4>
                  <p className="text-2xl font-bold text-primary">
                    {analysisResult.placeholders.length}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Form fields to configure
                  </p>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-medium text-foreground mb-2">
                    Page Layout
                  </h4>
                  <p className="text-sm text-foreground">
                    {analysisResult.layout.pageSize} •{" "}
                    {analysisResult.layout.orientation}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analysisResult.layout.columns} column(s)
                  </p>
                </div>

                <div className="p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-medium text-foreground mb-2">Images</h4>
                  <p className="text-2xl font-bold text-primary">
                    {analysisResult.images.length}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Image placeholders
                  </p>
                </div>
              </div>

              {analysisResult.placeholders.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-foreground mb-3">
                    Detected Placeholders:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {analysisResult.placeholders.map((placeholder) => (
                      <span
                        key={placeholder.id}
                        className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium"
                      >
                        {"{" + placeholder.name + "}"}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
